@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-benefits {
	&__list {
		@extend %reset-ul;
		@include mixins.grid-layout();
		gap: clamp(functions.spacing('lg'), calc(100 / 1920 * 100vw), 10rem) var(--grid-gutter);
	}
	&__item {
		@extend %reset-ul-li;
		grid-column: auto / span 12;
	}
	&__holder {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		max-width: 33rem;
		height: 100%;
		min-height: clamp(0rem, calc(203 / 1920 * 100vw), 20.3rem);
		padding-left: 1.8rem;
		border-left: 0.1rem solid variables.$color-black;
	}
	&__title {
		margin: 0 0 functions.spacing('lg');
	}

	// MQ
	@media (config.$xs-up) {
		&:has(.pp-24) &__item {
			grid-column: auto / span 6;
		}
	}
	@media screen and (min-width: 636px) {
		&__item {
			grid-column: auto / span 6;
		}
	}
	@media screen and (min-width: 1000px) {
		&:has(.pp-24) &__item,
		&__item {
			grid-column: auto / span 3;
		}
	}
}
