@use 'base/variables';

.b-header {
	$s: &;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 12;
	background: variables.$color-text;
	color: variables.$color-cream;
	opacity: 0;
	visibility: hidden;
	transition: opacity 0.15s ease-in-out, visibility 0.15s 0.15s ease-in-out;

	a {
		color: currentcolor;
	}

	.is-open & {
		opacity: 1;
		visibility: visible;
		transition: opacity 0.15s ease-in-out, visibility 0.15s ease-in-out;
	}
}
