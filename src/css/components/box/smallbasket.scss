@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-smallbasket {
	&__list {
		@extend %reset-ul;
		display: grid;
		grid-template-columns: auto 1fr repeat(3, max-content);
	}
	&__item {
		@extend %reset-ul-li;
		display: grid;
		grid-template-columns: subgrid;
		grid-column: auto / span 5;
		gap: 1rem;
		align-items: center;
		margin: 0 0 1rem;
		&:last-child {
			margin: 0;
		}
	}
	&__img {
		width: 4rem;
	}
	&__price {
		text-align: right;
	}
	&__remove {
		display: flex;
		text-decoration: none;
	}
}
