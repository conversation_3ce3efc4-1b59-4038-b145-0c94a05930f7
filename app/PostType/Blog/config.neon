parameters:
	config:
		blog:
			paging: 3 #temp low number - to testing

	postTypeRoutes:
		Blog: blog

cf:
	templates:
		blogLocalization:
			base:
				type: group
				items:
					annotation: @cf.definitions.annotation
					annotationDetail:
						type: textarea
						label: "Anotace pro detail"
					mainImage:
						type: image
						label: "<PERSON><PERSON><PERSON><PERSON> (min. DOPLNIT ROZMĚR)"

cc:
	templates:
		# ":Blog:Front:Blog:detail": []

application:
	mapping:
		Blog: App\PostType\Blog\*Module\Presenters\*Presenter

services:
	- App\PostType\Blog\Model\Orm\BlogLocalizationModel
	- App\PostType\Blog\AdminModule\Components\Form\BlogFormPrescription(coreFormPath: %appDir%/PostType/Core/AdminModule/Components/Form)
	- App\PostType\Blog\Model\BlogLocalizationFacade
	- App\PostType\Blog\AdminModule\Components\DataGrid\BlogDataGridPrescription
	- App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredDataFactory

	-
		implement: App\PostType\Blog\FrontModule\Components\Attached\AttachedBlogsFactory
		inject: true
