<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\Model\ElasticSearch\All\Convertor\Helper\CustomFieldHelper;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\StateRepository;
use Nette\Utils\Strings;

class ProductData implements Convertor
{

	public const KIND_PRODUCT = 'product';

	public function __construct(
		private MutationsHolder $mutationsHolder,
		private MutationHolder $mutationHolder,
		private PriceLevelRepository $priceLevelRepository,
		private StateRepository $stateRepository,
	)
	{
	}

	public function convert(object $object): array
	{
		assert($object instanceof Product);

		$product = $object;

		$this->mutationHolder->setMutation($this->mutationsHolder->getDefault());
		$mutation = $this->mutationHolder->getMutation();

		$product->setMutation($mutation);

		$priceGroup = $this->priceLevelRepository->getDefault();
		$state = $this->stateRepository->getDefault($mutation);

		$prices = [];
		$codes = [];
		$eans = [];
		foreach ($product->variants as $variant) {
			$prices[] = $variant->priceVat($mutation, $priceGroup, $state);

			if (!empty($variant->ean)) {
				$eans[] = Strings::lower((string) $variant->ean);
			}

			if (!empty($variant->code)) {
				$codes[] = Strings::lower((string) $variant->code);
			}
		}

		if (($brandParamValue = $product->getParameterValueByUid(Parameter::UID_MANUFACTURER)) !== null) {
			$brandParamValue = $brandParamValue->internalValue; /** @phpstan-ignore-line there is always only one manufacturer */
		}

		$categories = [];
		foreach ($product->inCategories as $category) {
			$categories[] = $category->id;
		}

		$ret = [
			'id' => $product->id,
			'name' => $product->name,
			'nameSort' => ($product->name !== null) ? mb_strtolower($product->name) : '',
			//'nameTitle' => $product->nameTitle,
			//'description' => $product->description,
			//'annotation' => $product->annotation,
			'type' => self::KIND_PRODUCT,

		];

		$filter = [
			'priceVat' => [
				$state->code => [
					$priceGroup->type => $prices,
				],
			],
			Parameter::UID_MANUFACTURER => $brandParamValue,
			'categories' => $categories,
			'codes' => $codes,
			'eans' => $eans,
			'isOld' => (int) $product->isOld,
			'isNew' => (int) $product->isNew,
			'isInStock' => (int) $product->isInStock,
			'publish' => $this->getPublishData($product),
			'publishDate' => [$product->publicFrom->getTimestamp(), $product->publicTo->getTimestamp()],
		];

		$ret['filter'] = $filter;

		$ret['kind'] = self::KIND_PRODUCT;

		$ret['repository'] = $object->getRepository()::class;
		list($imageIds, $fileIds) = CustomFieldHelper::getImagesIds($object);
		$ret['imageIds'] = $imageIds;
		$ret['fileIds'] = $fileIds;

		return $ret;
	}

	private function getPublishData(Product $product): array
	{
		$data = [];
		foreach ($product->productLocalizations as $productLocalization) {
			if ($productLocalization->public) {
				$data[] = sprintf('%s_public', $productLocalization->mutation->langCode);
			} else {
				$data[] = sprintf('%s_hide', $productLocalization->mutation->langCode);
			}
		}

		return $data;
	}

}
