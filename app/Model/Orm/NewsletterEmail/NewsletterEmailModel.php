<?php declare(strict_types = 1);

namespace App\Model\Orm\NewsletterEmail;

use App\Exceptions\UserException;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserRepository;
use App\Model\Orm\UserMutation\UserMutationRepository;
use Exception;

final class NewsletterEmailModel
{

	public function __construct(
		private readonly UserRepository $userRepository,
		private readonly UserMutationRepository $userMutationRepository,
	)
	{
	}

	/**
	 * @throws Exception
	 * @throws UserException
	 */
	public function subscribeEmail(string $email, Mutation $mutation): NewsletterEmail
	{
		if (($email = filter_var($email, FILTER_VALIDATE_EMAIL)) === false) {
			throw new Exception('Invalid email');
		}

		if ($this->isSubscribedEmail($email, $mutation)) {
			throw new UserException('Email already registered to newsletter');
		}

		return $this->newsletterEmailRepository->save(null, ['email' => $email, 'mutation' => $mutation]);
	}

	/**
	 * @return bool - false if the user is already registered, true if the user has been added to the newsletter
	 * @throws Exception
	 */
	public function subscribeUser(User $user, Mutation $mutation): bool
	{
		if (($userMutation = $user->userMutations->toCollection()->getBy(['mutation' => $mutation])) === null) {
			throw new UserException('User not registered in this mutation');
		}

		if (!$userMutation->newsletter) {
			$userMutation->newsletter = true;
			$this->userMutationRepository->persist($userMutation);
			return true;
		}

		return false;
	}

	public function unsubscribeEmail(string $email, Mutation $mutation): bool
	{
		if (($item = $this->newsletterEmailRepository->getEmail($email, $mutation)) !== null) {
			$this->newsletterEmailRepository->remove($item);
			return true;
		}

		return false;
	}

	public function unsubscribeUser(User $user, Mutation $mutation): bool
	{
		if (($userMutation = $user->userMutations->toCollection()->getBy(['mutation' => $mutation, 'newsletter' => true])) !== null) {
			$userMutation->newsletter = false;
			$this->userMutationRepository->persist($userMutation);
			return true;
		}

		return false;
	}

	public function isSubscribedEmail(string $email, Mutation $mutation): bool
	{
		return $this->newsletterEmailRepository->getEmail($email, $mutation) !== null;
	}

	public function isSubscribedUser(string $email, Mutation $mutation): bool
	{
		if (($user = $this->userRepository->getBy(['email' => $email])) !== null) {
			if (($userMutation = $user->userMutations->toCollection()->getBy(['mutation' => $mutation])) !== null) {
				return $userMutation->newsletter;
			}
		}

		return false;
	}

	public function create(string $email, Mutation $mutation): NewsletterEmail
	{
		$newNewsletterEmail = $this->subscribeEmail($email, $mutation);
		$this->newsletterEmailRepository->flush();

		return $newNewsletterEmail;
	}

}
