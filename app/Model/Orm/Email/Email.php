<?php declare(strict_types=1);

namespace App\Model\Orm\Email;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $email
 * @property DateTimeImmutable $createdTime {default now}
 * @property string $type {enum self::Type_*}
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$newsletterEmails}
 *
 * VIRTUAL
 */
abstract class Email extends Entity
{
	public const Type_Newsletter = 'newsletter';
	public const Type_Community = 'community';
}
