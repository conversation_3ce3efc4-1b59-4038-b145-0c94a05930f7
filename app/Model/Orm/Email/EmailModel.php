<?php declare(strict_types = 1);

namespace App\Model\Orm\Email;

use App\Exceptions\UserException;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserRepository;
use App\Model\Orm\UserMutation\UserMutationRepository;
use Exception;
use RuntimeException;

final readonly class EmailModel
{

	public function __construct(
		private EmailRepository $emailRepository,
		private UserRepository $userRepository,
		private UserMutationRepository $userMutationRepository,
	)
	{
	}

	/**
	 * @throws Exception
	 * @throws UserException
	 */
	public function subscribeNewsletterEmail(string $email, Mutation $mutation): NewsletterEmail
	{
		$this->validateEmail($email, $mutation, $type = Email::Type_Newsletter);

		return $this->emailRepository->save(new NewsletterEmail(), ['email' => $email, 'mutation' => $mutation]);
	}

	/**
	 * @throws Exception
	 * @throws UserException
	 */
	public function subscribeCommunityEmail(string $email, Mutation $mutation): NewsletterEmail
	{
		$this->validateEmail($email, $mutation, Email::Type_Community);

		return $this->emailRepository->save(new CommunityEmail(), ['email' => $email, 'mutation' => $mutation]);
	}

	/**
	 * @throws UserException
	 */
	private function validateEmail(string $email, Mutation $mutation, string $type): void
	{
		if (($email = filter_var($email, FILTER_VALIDATE_EMAIL)) === false) {
			throw new RuntimeException('Invalid email');
		}

		if ($this->isSubscribedEmail($email, $mutation, $type)) {
			throw new UserException('Email already registered to newsletter');
		}
	}

	/**
	 * @return bool - false if the user is already registered, true if the user has been added to the newsletter
	 * @throws Exception
	 */
	public function subscribeUser(User $user, Mutation $mutation): bool
	{
		if (($userMutation = $user->userMutations->toCollection()->getBy(['mutation' => $mutation])) === null) {
			throw new UserException('User not registered in this mutation');
		}

		if (!$userMutation->newsletter) {
			$userMutation->newsletter = true;
			$this->userMutationRepository->persist($userMutation);
			return true;
		}

		return false;
	}

	public function unsubscribeNewsletterEmail(string $email, Mutation $mutation): bool
	{
		return $this->unsubscribeEmail($email, $mutation, Email::Type_Newsletter);
	}

	public function unsubscribeCommunityEmail(string $email, Mutation $mutation): bool
	{
		return $this->unsubscribeEmail($email, $mutation, Email::Type_Community);
	}

	private function unsubscribeEmail(string $email, Mutation $mutation, string $type): bool
	{
		if (($item = $this->emailRepository->getEmail($email, $mutation, $type)) !== null) {
			$this->emailRepository->remove($item);
			return true;
		}

		return false;
	}

	public function unsubscribeUser(User $user, Mutation $mutation): bool
	{
		if (($userMutation = $user->userMutations->toCollection()->getBy(['mutation' => $mutation, 'newsletter' => true])) !== null) {
			$userMutation->newsletter = false;
			$this->userMutationRepository->persist($userMutation);
			return true;
		}

		return false;
	}

	public function isSubscribedEmail(string $email, Mutation $mutation, string $type): bool
	{
		return $this->emailRepository->getEmail($email, $mutation, $type) !== null;
	}

	public function isSubscribedUser(string $email, Mutation $mutation): bool
	{
		if (($user = $this->userRepository->getBy(['email' => $email])) !== null) {
			if (($userMutation = $user->userMutations->toCollection()->getBy(['mutation' => $mutation])) !== null) {
				return $userMutation->newsletter;
			}
		}

		return false;
	}

	/**
	 * @throws UserException
	 */
	public function createNewsletter(string $email, Mutation $mutation): NewsletterEmail
	{
		$newNewsletterEmail = $this->subscribeNewsletterEmail($email, $mutation);
		$this->emailRepository->flush();

		return $newNewsletterEmail;
	}

	/**
	 * @throws UserException
	 */
	public function createCommunity(string $email, Mutation $mutation): NewsletterEmail
	{
		$newNewsletterEmail = $this->subscribeCommunityEmail($email, $mutation);
		$this->emailRepository->flush();

		return $newNewsletterEmail;
	}

}
