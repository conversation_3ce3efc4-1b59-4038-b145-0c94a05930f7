<?php declare(strict_types = 1);

namespace App\Model\Orm\Email;

use App\Model\Orm\Mutation\Mutation;
use Elastica\Exception\NotFoundException;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;
use App\Model\Orm\Traits\HasSimpleSave;

/**
 * @method Email|CommunityEmail|NewsletterEmail|null getById($id)
 * @method Email|CommunityEmail|NewsletterEmail|null getBy(array $conds)
 * @method NewsletterEmail save(NewsletterEmail|CommunityEmail $entity, array $data)
 */
final class EmailRepository extends Repository
{

	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [Email::class, NewsletterEmail::class, CommunityEmail::class];
	}

	public function getEntityClassName(array $data): string
	{
		return $data['type'] === Email::Type_Newsletter ? NewsletterEmail::class : CommunityEmail::class;
	}

	public function getEmail(string $email, Mutation $mutation, string $type): ?Email
	{
		return $this->getBy(['email' => $email, 'mutation' => $mutation, 'type' => $type]);
	}

	public function getCommunityEmail(string $email, Mutation $mutation): ?CommunityEmail
	{
		return $this->getBy(['email' => $email, 'mutation' => $mutation, 'type' => Email::Type_Community]);
	}

	public function getNewsletterEmail(string $email, Mutation $mutation): ?NewsletterEmail
	{
		return $this->getBy(['email' => $email, 'mutation' => $mutation, 'type' => Email::Type_Newsletter]);
	}

	/**
	 * @return ICollection<NewsletterEmail>
	 */
	public function findAllNewsletterEmails(): ICollection
	{
		return $this->findBy(['type' => Email::Type_Newsletter]);
	}

	/**
	 * @throws NotFoundException
	 */
	public function getNewsletterEmailBy(array $conditions): NewsletterEmail
	{
		$conditions['type'] = Email::Type_Newsletter;
		$email = $this->getBy($conditions);

		if (!$email) {
			throw new NotFoundException('Email not found');
		}

		return $email;
	}

	public function getNewsletterEmailById(int $id): NewsletterEmail
	{
		$email = $this->getById($id);
		if (!$email) {
			throw new NotFoundException('Email not found');
		}

		return $email;
	}

}
