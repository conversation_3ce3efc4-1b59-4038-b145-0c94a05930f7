<?php

declare(strict_types=1);

namespace App\Model\Orm\DeliveryMethod;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\Traits\HasStaticCache;
use Brick\Money\Money;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class DeliveryMethodConfigurationMapper extends DbalMapper
{
	use HasCamelCase;
	use HasStaticCache;

	protected $tableName = 'delivery_method';

	public function getFreeDelivery(Mutation $mutation, State $state, PriceLevel $priceLevel): ?DeliveryMethodConfiguration
	{
		return $this->loadCache($this->createCacheKey('freeDelivery-', $mutation, $state, $priceLevel), function () use($mutation, $state, $priceLevel){
			$builder = $this->builder();
			$builder->select('[dm.*]');
			$builder->from('[' . $this->tableName . ']', 'dm');
			$builder->joinLeft('[delivery_method_x_state] AS [dmxs]','[dmxs.deliveryMethodId] = [dm.id]');
			$builder->joinLeft('[delivery_method_price] AS [dmp]','[dmp.deliveryMethodId] = [dm.id]');
			$builder->where('[dm.mutationId] = %i AND [dm.public] = 1 AND [dmxs.stateId] = %i AND [dmp.priceLevelId] = %i AND [dmp.stateId] = %i AND [dmp.freeFrom] IS NOT NULL', $mutation->id, $state->id, $priceLevel->id, $state->id);
			$builder->orderBy('[dmp.freeFrom] ASC');
			return  $this->toEntity($builder);
		});
	}

	public function getFreeDeliveryAmount(Mutation $mutation, State $state, PriceLevel $priceLevel): ?Money
	{
		$freeDelivery = $this->getFreeDelivery($mutation, $state, $priceLevel);
		if ($freeDelivery === null) {
			return null;
		}

		$price = $freeDelivery->getPrice($priceLevel, $state);

		return Money::of($price->freeFrom, $price->price->currency);
	}

	/**
	 * @return ICollection<DeliveryMethodConfiguration>
	 */
	public function getAvailable(Mutation $mutation, State $state, PriceLevel $priceLevel): ICollection
	{
		return $this->loadCache($this->createCacheKey('available-', $mutation, $state, $priceLevel), function () use($mutation, $state, $priceLevel){
			$builder = $this->builder();
			$builder->select('[dm.*]');
			$builder->from('[' . $this->tableName . ']', 'dm');
			$builder->joinLeft('[delivery_method_x_state] AS [dmxs]','[dmxs.deliveryMethodId] = [dm.id]');
			$builder->joinLeft('[delivery_method_price] AS [dmp]','[dmp.deliveryMethodId] = [dm.id]');
			$builder->where('[dm.mutationId] = %i AND [dm.public] = 1 AND [dmxs.stateId] = %i AND [dmp.priceLevelId] = %i AND [dmp.stateId] = %i', $mutation->id, $state->id, $priceLevel->id, $state->id);
			$builder->addOrderBy('[dm.sort] ASC');

			return  $this->toCollection($builder);
		});
	}
}
