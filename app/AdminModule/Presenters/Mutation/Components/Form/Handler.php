<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Mutation\Components\Form;

use App\Model\CustomField\CustomFields;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\AdminModule\Presenters\Mutation\Components\Form\FormData\BaseFormData;
use App\Model\Orm\User\User;
use Nette\Utils\ArrayHash;

final class Handler
{

	public function __construct(
		private readonly MutationRepository $mutationRepository,
		private readonly CustomFields $customFields,
	)
	{
	}

	public function handle(Mutation $mutation, BaseFormData $data, User $user): void
	{
		if ($user->isDeveloper()) {
			$mutation->name = $data->name;
			$mutation->langCode = $data->langCode;
		}

		$mutation->adminEmail = $data->adminEmail;
		$mutation->contactEmail = $data->contactEmail;
		$mutation->orderEmail = $data->orderEmail;
		$mutation->fromEmail = $data->fromEmail;
		$mutation->fromEmailName = $data->fromEmailName;
		$mutation->heurekaOverenoKey = $data->heurekaOverenoKey;

		if (isset($mutation->cf)) {
			if (isset($data->setup->cf) && $data->setup->cf !== '') {
				$mutation->setCf($this->customFields->prepareDataToSave($data->setup->cf));
			} else {
				$mutation->setCf(new ArrayHash());
			}
		}
		$this->mutationRepository->persistAndFlush($mutation);
	}

}
