<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Newsletter\Components\DataGrid;

use App\Model\Orm\Email\Email;
use App\Model\Orm\Email\EmailRepository;
use App\Model\Orm\Orm;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use Nette\Application\UI\Control;

class DataGrid extends Control
{

	use HasMutationColumn;

	public function __construct(
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly EmailRepository $emailRepository,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$collection = $this->emailRepository->findAll();
		$grid->setItemsPerPageList([30, 50]);

		$grid->setDataSource($collection);
		$grid->addColumnText('email', 'email')->setSortable()->setFilterText();

		$grid->addColumnText('type', 'type')
			->setSortable()
			->setFilterSelect(Email::Types)
			->setPrompt($this->translator->translate('all'));

		$grid->addColumnDateTime('createdTime', 'date_created')
			->setSortable()
			->setFormat('Y-m-d H:i:s')
			->setFilterDateRange();

		$this->addColumnMutation($grid);

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		$grid->addExportCsv('Csv export (filtered)', 'subscribers.csv', 'UTF-8', ';', false, true);
		$grid->addExportCsv('Csv export (filtered) for windows', 'subscribers.csv', 'windows-1250', ';', false, true);

		return $grid;
	}

}
