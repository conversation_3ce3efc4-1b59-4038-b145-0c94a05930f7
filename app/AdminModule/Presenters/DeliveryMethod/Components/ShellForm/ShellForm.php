<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\DeliveryMethod\Components\ShellForm;

use App\AdminModule\Presenters\DeliveryMethod\Components\ShellForm\FormData\BaseFormData;
use App\Model\ConfigService;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\DeliveryMethod\DeliveryMethodRegistry;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\Random;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
class ShellForm extends Control
{

	/**
	 * @var ICollection<Mutation>
	 */
	private ICollection $mutations;

	public function __construct(
		private readonly Translator $translator,
		private readonly MutationRepository $mutationRepository,
		private readonly DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
		private readonly DeliveryMethodRegistry $deliveryMethodRegistry,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$this->mutations = $this->mutationRepository->findBy([]);
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutations', $this->mutations);

		$template->render(__DIR__ . '/shellForm.latte');
	}

	private function getDeliveryIdentifiers(): array
	{
		$deliveries = [];
		foreach ($this->deliveryMethodRegistry->list() as $deliveryMethod) {
			$deliveries[$deliveryMethod->getUniqueIdentifier()] = $deliveryMethod->getUniqueIdentifier();
		}

		return $deliveries;
	}

	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		$form->addSelect('mutation', 'select_mutation', $this->mutations->fetchPairs('id', 'name'))->setRequired();
		$form->addSelect('deliveryIdentificator', 'select_delivery_identificator', $this->getDeliveryIdentifiers())->setRequired();
		$form->addSubmit('send', 'send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		$form->onValidate[] = [$this, 'formValidate'];
		return $form;
	}

	public function formValidate(Form $form, BaseFormData $data): void
	{
		$exists = $this->deliveryMethodConfigurationRepository->findBy(['mutation' => $data->mutation, 'deliveryMethodUniqueIdentifier' => $data->deliveryIdentificator])->countStored();

		if ($exists > 0) {
			$form->addError('delivery_already_exists_in_mutation');
		}
	}

	public function formError(Form $form): void
	{
		$this->presenter->flashMessage('Error: '.implode(', ', $form->getErrors()), 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): void
	{
		$deliveryMethod = new DeliveryMethodConfiguration();
		$deliveryMethod->mutation = $this->mutations->getById($data->mutation);
		$deliveryMethod->deliveryMethodUniqueIdentifier = $data->deliveryIdentificator;

		$this->deliveryMethodConfigurationRepository->persistAndFlush($deliveryMethod);

		$this->presenter->redirect('edit', ['id' => $deliveryMethod->id]);
	}

}
