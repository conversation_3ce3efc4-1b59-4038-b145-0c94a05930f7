<?php

declare(strict_types=1);

namespace App\Api\V1\Forms\Newsletter;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use App\Api\V1\Forms\BaseFormController;
use App\FrontModule\Components\NewsletterForm\NewsletterFormFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;

#[Path('/')]
#[Tag('noAuthentication'), Tag('Form')]
final class NewsletterFormController extends BaseFormController
{
	public function __construct(
		private readonly MutationHolder $mutationHolder,
		private readonly MutationsHolder $mutationsHolder,
		private readonly NewsletterFormFactory $newsletterFormFactory,
	) {}

	#[Path('/newsletter')]
	#[Method('POST')]
	#[Response(description: 'Success', code: '204')]
	#[Response(description: 'Form data invalid', code: '422')]
	public function submit(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$mutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($mutation);

		$form = $this->newsletterFormFactory->create(standalone: true);
		$form->allowCrossOrigin();

		$this->fakeSubmit($form);
		$form->fireEvents();

		if ($form->isSuccess()) {
			return $response->withStatus(ApiResponse::S204_NO_CONTENT);
		}

		return $this->jsonResponse(['errors' => $form->getErrors()], $response)
			->withStatus(ApiResponse::S422_UNPROCESSABLE_ENTITY);
	}
}
