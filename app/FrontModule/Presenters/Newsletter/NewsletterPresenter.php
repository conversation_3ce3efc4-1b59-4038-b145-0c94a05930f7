<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Newsletter;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\Email\EmailModel;
use App\Model\Orm\User\User;
use Nette\Application\AbortException;
use Nette\Application\Attributes\Persistent;

final class NewsletterPresenter extends BasePresenter
{

	#[Persistent]
	public ?string $state;

	/** @var string[][]  */
	private array $stateMessageList = [
		'success' => ['Byli jste úspěšně odhlášeni z newsletteru.', 'ok'],
		'already' => ['Z newsletteru jste již byli odhlášeni dříve.', 'info'],
		'error' => ['Tento odkaz je neplatný.', 'error'],
	];

	public function __construct(
		private readonly EmailModel $emailModel,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();

		if (isset($this->params['idref'])) {
			$idref = $this->params['idref'];
			$this->setObject($this->orm->tree->getById($idref));
		}
	}

	/**
	 * @throws AbortException
	 */
	public function actionUnsubscribe(string $email, string $hash): void
	{
		/**
		 * @todo: hash !?
		 */
		if (isset($this->state, $this->stateMessageList[$this->state])) {
			$this->template->stateMessage = $this->stateMessageList[$this->state];
			return;
		}

		$state = 'already';

		if ($hash === 'testHash') {
			$state = 'success';
		} else {
			if ($this->emailModel->unsubscribeNewsletterEmail($email, $this->mutation)) {
				$state = 'success';
			}

			if ((($user = $this->orm->user->getBy(['email' => $email])) !== null) && $this->emailModel->unsubscribeUser($user, $this->mutation)) {
				$state = 'success';
			}
		}

		$this->redirect('unsubscribe', ['state' => $state]); // 'unsubscribe' intentionally; 'this' doesn't null params
	}

}
