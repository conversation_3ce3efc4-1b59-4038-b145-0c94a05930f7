{default $class = 'u-mb-lg'}

<div n:if="$productParameters->count() > 0" n:class="b-parameters, $class">
	<h2 class="b-parameters__title">
		{_title_parameters}
	</h2>

	<dl class="b-parameters__dl">
		{foreach $productParameters as $parameter}
			{var $parameterValue = $product->getParameterValueByUid($parameter->uid)}
			<dt class="b-parameters__dt">
				{$parameter->title}:
			</dt>
			{if $parameter->type == "wysiwyg"}
				<dd class="b-parameters__dd">
					{$parameterValue->value|noescape}
				</dd>
			{elseif $parameter->type == "bool"}
				{if $parameterValue->value == 1}
					<dd class="b-parameters__dd">
						{_yes}
					</dd>
				{else}
					<dd class="b-parameters__dd">
						{_no}
					</dd>
				{/if}
			{else}
				<dd class="b-parameters__dd">
					{if $parameter->type == "multiselect"}
						{foreach $parameterValue as $valueObject}
							{if in_array($parameter->uid, $filtrableUids)}
								{var $filter = ['dials' => [$parameter->uid => [$valueObject->id => $valueObject->id]]]}
								{capture $link}{plink $mainCategory, 'filter' => $filter}{/capture}
								{php $link = urldecode(htmlspecialchars_decode($link))}

								<a href="{$link}">
									{$valueObject->value}{if !$iterator->last}, {/if}
								</a>
							{else}
								{$valueObject->value}{if !$iterator->last}, {/if}
							{/if}
						{/foreach}
					{else}
						{if in_array($parameter->uid, $filtrableUids)}
							{var $filter = ['dials' => [$parameter->uid => [$parameterValue->id => $parameterValue->id]]]}
							{capture $link}{plink $mainCategory, 'filter' => $filter}{/capture}
							{php $link = urldecode(htmlspecialchars_decode($link))}

							<a href="{$link}">
								{$parameterValue->value} {if $parameter->type == 'number'}{$parameter->unit}{/if}
							</a>
						{else}
							{$parameterValue->value} {if $parameter->type == 'number'}{$parameter->unit}{/if}
						{/if}

					{/if}
				</dd>
			{/if}
		{/foreach}
	</dl>
</div>
