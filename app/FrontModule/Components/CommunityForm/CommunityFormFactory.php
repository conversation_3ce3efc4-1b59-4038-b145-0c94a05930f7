<?php

declare(strict_types=1);

namespace App\FrontModule\Components\NewsletterForm;

use App\Exceptions\UserException;
use App\Model\Email\CommonFactory;
use App\Model\Form\CommonFormFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Email\EmailModel;
use App\Model\Orm\Orm;
use Nette\Application\UI\Form as UIForm;
use Nette\Forms\Form;
use Nette\Utils\ArrayHash;
use Throwable;

final readonly class NewsletterFormFactory
{
	public function __construct(
		private CommonFormFactory $formFactory,
		private MutationHolder $mutationHolder,
		private EmailModel $emailModel,
		private CommonFactory $commonEmailFactory,
		private Orm $orm,
	) {}

	public function create(bool $standalone = false): Form
	{
		$form = $this->formFactory->create($standalone ? Form::class : UIForm::class);

		$form->addText('email', 'form_label_email')
			->addRule(Form::EMAIL)
			->setHtmlType('email')
			->setRequired();

		$form->addSubmit('send');

		$form->onSuccess[] = function (Form $form, ArrayHash $values): void {
			try {
				$this->emailModel->subscribeNewsletterEmail($values->email, $this->mutationHolder->getMutation());
				$this->orm->flush();
				$this->commonEmailFactory
					->create()
					->send('', $values->email, 'newsletterInfo', (array) $values);
			} catch (UserException) {
				// no-op, already subscribed
			} catch (Throwable) {
				$form->addError('newsletterError');
			}
		};

		return $form;
	}
}
