<?php declare(strict_types = 1);

/** @noinspection PhpRedundantCatchClauseInspection */

/** @noinspection PhpUnusedParameterInspection */

namespace App\FrontModule\Components\RegistrationForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Email\EmailModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Security\Acl;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Security\AuthenticationException;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 */
final class RegistrationForm extends UI\Control
{

	private Mutation $mutation;

	public function __construct(
		private readonly Tree $object,
		private readonly UserModel $userModel,
		private readonly \App\Model\Email\CommonFactory $commonEmailFactory,
		private readonly EmailModel $emailModel,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		MutationHolder $mutationHolder,
		private readonly MessageForFormFactory $messageForFormFactory,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->render(__DIR__ . '/registrationForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addText('firstname', 'form_label_firstname')->setRequired();
		$form->addText('lastname', 'form_label_lastname')->setRequired();
		$form->addEmail('email', 'form_label_email')
			->setRequired('E-mail is required');
		$form->addPassword('password', 'form_label_password')
			->setRequired();
		$form->addPassword('passwordVerify', 'form_label_password2')
			->setRequired()
			->addRule(UI\Form::EQUAL, 'form_password_not_same', $form['password']);
		$form->addCheckbox('agree')->setRequired();
		$form->addCheckbox('isNewsletter', $this->translator->translate('form_label_newsletter'))->setTranslator(null)
			->setDefaultValue(1);

		$form->addSubmit('save', 'btnRegister');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onValidate[] = [$this, 'editFormValidate'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function editFormValidate(UI\Form $form, ArrayHash $values): void
	{
		$user = $this->userModel->getByEmail($values->email, $this->mutation);

		if ($user) {
			$link1 = $this->presenter->link($this->mutation->pages->userLogin, ['email' => $values->email]);
			$link2 = $this->presenter->link($this->mutation->pages->lostPassword, ['email' => $values->email]);
			$strTranslated = $this->translator->translate('mail_exist_register');

			if (strpos($strTranslated, '%link1%') !== false) {
				$strTranslated = str_replace('%link1%', $link1, $strTranslated);
			}

			if (strpos($strTranslated, '%link2%') !== false) {
				$strTranslated = str_replace('%link2%', $link2, $strTranslated);
			}

			$form->addError($strTranslated, false);
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();

		$user = new User();
		$this->orm->user->attach($user);
		$user->role = Acl::ROLE_USER;
		$user->priceLevel = PriceLevel::DEFAULT_ID;
		$user->mutations->add($this->mutation);

		$user = $this->userModel->save($user, $valuesAll);

		try {
			$this->commonEmailFactory
				->create()
				->send('', $user->email, 'login', (array) $valuesAll);

			if (!empty($valuesAll['isNewsletter'])) {
				$this->emailModel->subscribeUser($user, $this->mutation);
				$this->orm->flush();
			}

			$this->presenter->getUser()->login($valuesAll['email'], $valuesAll['password']);

			$this->flashMessage('form_register_ok', 'ok');
			$this->presenter->redirect($this->mutation->pages->userSection);

		} catch (AuthenticationException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
