{default $class = false}
{default $toggle = 'link'}
{default $link = false}
{default $file = false} {* todo *}
{default $poster = false}
{default $autoplay = false}
{default $aspectRatio = '16-9'}
{default $posterSize = 'xl'}
{default $loop = false}
{default $type = 'lite'}
{default $controls = true}

{if $autoplay}
	{php $loop = true}
	{php $type = 'normal'}
	{php $controls = false}
{/if}

<div n:if="$toggle && ($link || $file)" n:class="video, $class"{if $toggle == 'link' && $type == 'normal'} data-controller="video"{/if}>
	<div class="img img--{$aspectRatio}">
		{if $toggle == 'link'}
			{* Odkaz na Youtube / Vimeo *}
			{if strpos($link, 'youtube')}
				{php $autoplay = $autoplay ? 1 : 0}
				{php $urlObject =  new \Nette\Http\Url($link)}
				{php $id = $urlObject->getQueryParameter('v')}
				{php $autoplay = $autoplay ? 1 : 0}
				{php $controls = $controls ? 1 : 0}
				{php $loop = $loop ? 1 : 0}

				{if $type == 'lite'}
					<lite-youtube class="img__media" videoid="{$id}"{if $poster} style="background-image: url('{$poster->getSize($posterSize)->src|noescape}');"{/if}></lite-youtube>
				{else}
					<div class="img__media"
						data-video-target="video"
						data-type="youtube"
						data-id="{$id}"
						data-options='{"controls": {$controls}, "showinfo": 0, "related": 0, "rel": 0, "modestbranding": 1, "annotation": 0, "autoplay": {$autoplay}, "mute": {$autoplay} }'
						{if $autoplay} data-autoplay="true"{/if}
						{if $loop} data-loop="true"{/if}
						{if $poster} style="background-image: url('{$poster->getSize($posterSize)->src|noescape}');"{/if}>
					</div>
				{/if}
			{else}
				{var $id = pathinfo(explode('/', $link)[count(explode('/', $link))-1], PATHINFO_FILENAME)}
				{php $autoplay = $autoplay ? "true" : "false"}
				{php $controls = $controls ? "true" : "false"}
				{php $loop = $loop ? "true" : "false"}

				{if $type == 'lite'}
					{* todo bug lite-vimeo background image doesn't work *}
					<lite-vimeo class="img__media" videoid="{$id}"{if $poster} style="background-image: url('{$poster->getSize($posterSize)->src|noescape}');"{/if}></lite-vimeo>
				{else}
					<div class="img__media"
						data-video-target="video"
						data-type="vimeo"
						data-id="{$id}"
						data-options='{"controls": {$controls}, "loop": {$loop}, "autoplay": {$autoplay}, "muted": {$autoplay}}'
						{if $autoplay == "true"} data-autoplay="true"{/if}
						{if $loop == "true"} data-loop="true"{/if}
						{if $poster} style="background-image: url('{$poster->getSize($posterSize)->src|noescape}');"{/if}>
					</div>
				{/if}
			{/if}
		{elseif $toggle == 'file'}
			{* Soubor *}
			<video class="img__media"{if $autoplay} autoplay loop muted playsinline{else} controls{/if}{if $poster} poster="{$poster->getSize($posterSize)->src}"{/if} data-controller="toggle-class" data-action="play->toggle-class#toggle stop->toggle-class#toggle pause->toggle-class#toggle" data-toggle-class="is-playing" data-toggle-closest=".video">
				<source src="https://superadmin-stage.www6.superkoderi.cz/{$file->url}" type="video/mp4">
			</video>
		{/if}
	</div>
	<span class="video__play play">
		<span class="u-vhide">{_"btn_play"}</span>
	</span>
</div>