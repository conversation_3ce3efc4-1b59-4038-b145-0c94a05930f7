{default $class = 'u-mb-3xl'}
{default $cf = $object->cf->events}

<section n:class="c-events, $class">
	<div class="row-main">
		<h2 n:if="$cf->title1 ?? false" class="c-events__title pp-18 u-title-line">{$cf->title1}</h2>

		<div class="c-events__carousel embla" data-controller="embla">
			<div class="c-events__top u-mb-sm">
				<p n:if="$cf->title2 ?? false" class="c-events__title2 h3 u-mb-0">{$cf->title2}</p>
				<p class="c-events__tools u-mb-0">
					<button class="btn btn--bd btn--icon" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
						<span class="btn__text">
							{('arrow-left')|icon}
							<span class="u-vhide">{_btn_prev}</span>
						</span>
					</button>
					<button class="btn btn--bd" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
						<span class="btn__text">
							{_btn_next}
							{('arrow-right')|icon}
						</span>
					</button>
				</p>
			</div>

			<div class="embla__viewport" data-embla-target="viewport">
				<div class="c-events__list grid grid--scroll grid--y-0 embla__container">
					<div n:for="$i = 0; $i < 5; $i++" class="c-events__item grid__cell">
						{include $templates.'/part/box/event.latte', class: false}
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
