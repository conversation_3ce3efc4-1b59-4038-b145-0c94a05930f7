{default $contacts = $object->mutation->cf->contacts ?? false}
{default $socials = $object->mutation->cf->socials ?? false}

<div class="footer__spacer"></div>
<footer class="footer">
	<div class="row-main">
		<h2 class="footer__title">{_"footer_title"|noescape}</h2>

		<div class="footer__details" n:if="$contacts" n:ifcontent>
			<div n:ifcontent class="grid">
				<div n:if="$contacts->phone ?? false" class="grid__cell size--3-12">
					<p class="u-mb-0">
						<span class="footer__details-label u-d-b">{_"footer_phone"}</span>
						<a href="tel:{$contacts->phone|replace:' ',''}" class="footer__details-link">{$contacts->phone}</a>
					</p>
				</div>
				<div n:if="$contacts->email ?? false" class="grid__cell size--3-12">
					<p class="u-mb-0">
						<span class="footer__details-label u-d-b">{_"footer_mail"}</span>
						<a href="mailto:{$contacts->email}" class="footer__details-link">{$contacts->email}</a>
					</p>
				</div>
				<div class="grid__cell size--5-12 u-ml-auto">
					{control newsletterForm}
				</div>
			</div>
		</div>

		<div class="footer__bottom">
			<div class="grid">
				<p class="grid__cell size--6-12">
					{_"copyright"}
					<button type="button" class="as-link u-d-b" data-cookie-open>{_"btn_cookies_setting"}</button>
				</p>
				<nav class="m-footer grid__cell size--5-12 u-ml-auto">
					<ul class="m-footer__list">
						{php $list = $object->mutation->cf?->footerMenu?->footer_menu?->list ?? false}
						{php $social = $object->mutation->cf?->footerMenu?->social ?? false}

						{if is_array($list)}
							<li n:foreach="$list as $item" n:ifcontent class="m-footer__item">
								{php $type = $item->toggle}
								{php $page = isset($item->systemHref) && isset($item->systemHref->page) ? $item->systemHref->page->getEntity() ?? false : false}
								{php $hrefName = ($item->systemHref??->hrefName ?? false) ?: ($item->customHref??->hrefName ?? false)}
								{php $href = $item->customHref??->href ?? false}

								{if $type == 'systemHref' && $page}
									<a href="{plink $page}" n:ifcontent class="m-footer__link">
										{if $hrefName}
											{$hrefName}
										{else}
											{$page->nameAnchor}
										{/if}
									</a>
								{elseif $type == 'customHref' && $href && $hrefName}
									<a href="{$href}" class="m-footer__link" target="_blank" rel="noopener noreferrer">
										{$hrefName}
									</a>
								{/if}
							</li>
						{/if}

						{if $socials}
							<li n:if="$socials->instagram ?? false" class="m-footer__item">
								<a href="{$socials->instagram}" class="m-footer__link" target="_blank" rel="noopener noreferrer">Instagram</a>
							</li>
							<li n:if="$socials->facebook ?? false" class="m-footer__item">
								<a href="{$socials->facebook}" class="m-footer__link" target="_blank" rel="noopener noreferrer">Facebook</a>
							</li>
							<li n:if="$socials->linkedin ?? false" class="m-footer__item">
								<a href="{$socials->linkedin}" class="m-footer__link" target="_blank" rel="noopener noreferrer">LinkedIn</a>
							</li>
						{/if}
					</ul>
				</nav>
			</div>
		</div>
	</div>
</footer>
