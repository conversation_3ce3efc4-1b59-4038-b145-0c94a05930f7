extensions:
	api: Apitte\Core\DI\ApiExtension
	nettrine.annotations: Nettrine\Annotations\DI\AnnotationsExtension
	nettrine.cache: Nettrine\Cache\DI\CacheExtension
	validator: Contributte\Validator\DI\ValidatorExtension

api:
	debug: %debugMode%
	catchException: true
	plugins:

		Apitte\Debug\DI\DebugPlugin:
			debug:
				panel: %debugMode%
				negotiation: %debugMode%
		Apitte\Middlewares\DI\MiddlewaresPlugin:
			tracy: true
			autobasepath: true
		Apitte\Core\DI\Plugin\CoreDecoratorPlugin:
		Apitte\Negotiation\DI\NegotiationPlugin:
		Apitte\Core\DI\Plugin\CoreMappingPlugin:
			request:
				validator: @symfonyValidator
		Apitte\OpenApi\DI\OpenApiPlugin:
			swaggerUi:
				panel: %debugMode% #activate Tracy panel in debug mode
				url: null # default url
				expansion: list # list|full|none
				filter: true # true|false|string
				title: Api V1



nettrine.annotations:
  debug: %debugMode%

services:
	nettrine.annotations: Nettrine\Annotations\DI\AnnotationsExtension
	nettrine.cache: Nettrine\Cache\DI\CacheExtension

	symfonyValidator:
		factory: Apitte\Core\Mapping\Validator\SymfonyValidator
		setup:
			- setConstraintValidatorFactory(Contributte\Validator\ContainerConstraintValidatorFactory())

	decorator.request.authentication:
		class: App\Api\Decorator\RequestAuthentication
		tags: [apitte.core.decorator: [priority: 1, type: handler.before]]

	decorator.request.findMutation:
		class: App\Api\Decorator\MutationFinder
		tags: [apitte.core.decorator: [priority: 2, type: handler.before]]

search:
	apiControllers:
		in: %appDir%
		implements: Apitte\Core\UI\Controller\IController

	apiValidators:
		in: %appDir%/Api/Validator
		classes:
			- *Validator
